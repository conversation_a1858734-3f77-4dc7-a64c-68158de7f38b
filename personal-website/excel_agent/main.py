import sys
import subprocess
from PyQt6.QtWidgets import <PERSON>A<PERSON><PERSON>, QMain<PERSON>indow, QPushButton, QVBoxLayout, QWidget, QMessageBox

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Spreadsheet Launcher Agent")
        self.setGeometry(100, 100, 300, 100)  # x, y, width, height

        # Create a button
        self.button = QPushButton("Open Spreadsheet Program", self)
        self.button.clicked.connect(self.open_spreadsheet)

        # Set up layout
        layout = QVBoxLayout()
        layout.addWidget(self.button)

        container = QWidget()
        container.setLayout(layout)
        self.setCentralWidget(container)

    def open_spreadsheet(self):
        # Commands to try for opening a spreadsheet program on Linux
        # LibreOffice Calc is a common one. xdg-open tries the default app.
        commands_to_try = [
            ['libreoffice', '--calc'],
            ['xdg-open', 'application/vnd.oasis.opendocument.spreadsheet'] # Tries to open default for .ods
            # Add other commands if needed, e.g., for Gnumeric or other specific apps
        ]

        opened_successfully = False
        for cmd in commands_to_try:
            try:
                subprocess.Popen(cmd)
                opened_successfully = True
                print(f"Attempting to open spreadsheet with: {' '.join(cmd)}")
                break  # Exit loop if successful
            except FileNotFoundError:
                print(f"Command not found: {' '.join(cmd)}")
            except Exception as e:
                print(f"Error opening spreadsheet with {' '.join(cmd)}: {e}")
        
        if not opened_successfully:
            QMessageBox.warning(self, "Error", "Could not find or open a spreadsheet program.\nPlease ensure LibreOffice Calc or a similar program is installed and in your PATH.")

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec())
