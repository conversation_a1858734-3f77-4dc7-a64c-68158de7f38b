# AI Agent - Spreadsheet Launcher

This is a simple Python application that provides a button to launch a spreadsheet program (e.g., LibreOffice Calc) on a Linux system.

## Setup

1.  **Navigate to the project directory:**
    ```bash
    cd excel_agent
    ```

2.  **Create a virtual environment (optional but recommended):**
    ```bash
    python3 -m venv venv
    ```

3.  **Activate the virtual environment:**
    ```bash
    source venv/bin/activate
    ```
    (On Windows, it would be `venv\Scripts\activate`)

4.  **Install dependencies:**
    ```bash
    pip install -r requirements.txt
    ```

## Running the Application

Once the setup is complete, you can run the application with:

```bash
python3 main.py
```

This will open a small window with a button. Clicking the button will attempt to launch your default spreadsheet application or LibreOffice Calc.
