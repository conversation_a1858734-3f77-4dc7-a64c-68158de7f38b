<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>iPhone 15 Pro Max Shell</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@3.4.1/dist/tailwind.min.css" rel="stylesheet">
  <style>
    .iphone-shell {
      width: 430px; /* iPhone 15 Pro Max 逻辑宽度 */
      height: 932px; /* iPhone 15 Pro Max 逻辑高度 */
      border-radius: 48px;
      box-shadow: 0 8px 32px rgba(0,0,0,0.25), 0 1.5px 0 #ccc inset;
      background: #f8f8fa;
      border: 4px solid #d1d5db;
      position: relative;
      overflow: hidden;
      margin: 0 auto;
    }
    .notch {
      position: absolute;
      top: 16px;
      left: 50%;
      transform: translateX(-50%);
      width: 120px;
      height: 32px;
      background: #222;
      border-radius: 16px;
      z-index: 10;
    }
    .side-btn {
      position: absolute;
      width: 6px;
      height: 48px;
      background: #bbb;
      border-radius: 3px;
      z-index: 20;
    }
    .side-btn.left { left: -10px; top: 120px; }
    .side-btn.right { right: -10px; top: 220px; }
    .volume-btn { left: -10px; top: 200px; height: 32px; }
    .power-btn { right: -10px; top: 320px; height: 32px; }
    .screen {
      position: absolute;
      top: 32px;
      left: 0;
      width: 100%;
      height: calc(100% - 32px);
      background: #fff;
      border-radius: 44px;
      overflow: hidden;
      z-index: 5;
      display: flex;
      flex-direction: column;
    }
  </style>
</head>
<body class="bg-gray-100 flex items-center justify-center min-h-screen">
  <div class="iphone-shell">
    <div class="notch"></div>
    <div class="side-btn left"></div>
    <div class="side-btn volume-btn"></div>
    <div class="side-btn right"></div>
    <div class="side-btn power-btn"></div>
    <div class="screen">
      <!-- 页面内容插槽 -->
      <slot></slot>
    </div>
  </div>
</body>
</html> 